<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>sync_mpe_config</name>
    <description>同步mpe_config表从DM到MySQL</description>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field><id>ID_BATCH</id><enabled>Y</enabled><name>ID_BATCH</name></field>
        <field><id>CHANNEL_ID</id><enabled>Y</enabled><name>CHANNEL_ID</name></field>
        <field><id>TRANSNAME</id><enabled>Y</enabled><name>TRANSNAME</name></field>
        <field><id>STATUS</id><enabled>Y</enabled><name>STATUS</name></field>
        <field><id>LINES_READ</id><enabled>Y</enabled><name>LINES_READ</name><subject/></field>
        <field><id>LINES_WRITTEN</id><enabled>Y</enabled><name>LINES_WRITTEN</name><subject/></field>
        <field><id>LINES_UPDATED</id><enabled>Y</enabled><name>LINES_UPDATED</name><subject/></field>
        <field><id>LINES_INPUT</id><enabled>Y</enabled><name>LINES_INPUT</name><subject/></field>
        <field><id>LINES_OUTPUT</id><enabled>Y</enabled><name>LINES_OUTPUT</name><subject/></field>
        <field><id>LINES_REJECTED</id><enabled>Y</enabled><name>LINES_REJECTED</name><subject/></field>
        <field><id>ERRORS</id><enabled>Y</enabled><name>ERRORS</name></field>
        <field><id>STARTDATE</id><enabled>Y</enabled><name>STARTDATE</name></field>
        <field><id>ENDDATE</id><enabled>Y</enabled><name>ENDDATE</name></field>
        <field><id>LOGDATE</id><enabled>Y</enabled><name>LOGDATE</name></field>
        <field><id>DEPDATE</id><enabled>Y</enabled><name>DEPDATE</name></field>
        <field><id>REPLAYDATE</id><enabled>Y</enabled><name>REPLAYDATE</name></field>
        <field><id>LOG_FIELD</id><enabled>Y</enabled><name>LOG_FIELD</name><subject/></field>
        <field><id>EXECUTING_SERVER</id><enabled>N</enabled><name>EXECUTING_SERVER</name></field>
        <field><id>EXECUTING_USER</id><enabled>N</enabled><name>EXECUTING_USER</name></field>
        <field><id>CLIENT</id><enabled>N</enabled><name>CLIENT</name></field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field><id>ID_BATCH</id><enabled>Y</enabled><name>ID_BATCH</name></field>
        <field><id>SEQ_NR</id><enabled>Y</enabled><name>SEQ_NR</name></field>
        <field><id>LOGDATE</id><enabled>Y</enabled><name>LOGDATE</name></field>
        <field><id>TRANSNAME</id><enabled>Y</enabled><name>TRANSNAME</name></field>
        <field><id>STEPNAME</id><enabled>Y</enabled><name>STEPNAME</name></field>
        <field><id>STEP_COPY</id><enabled>Y</enabled><name>STEP_COPY</name></field>
        <field><id>LINES_READ</id><enabled>Y</enabled><name>LINES_READ</name></field>
        <field><id>LINES_WRITTEN</id><enabled>Y</enabled><name>LINES_WRITTEN</name></field>
        <field><id>LINES_UPDATED</id><enabled>Y</enabled><name>LINES_UPDATED</name></field>
        <field><id>LINES_INPUT</id><enabled>Y</enabled><name>LINES_INPUT</name></field>
        <field><id>LINES_OUTPUT</id><enabled>Y</enabled><name>LINES_OUTPUT</name></field>
        <field><id>LINES_REJECTED</id><enabled>Y</enabled><name>LINES_REJECTED</name></field>
        <field><id>ERRORS</id><enabled>Y</enabled><name>ERRORS</name></field>
        <field><id>INPUT_BUFFER_ROWS</id><enabled>Y</enabled><name>INPUT_BUFFER_ROWS</name></field>
        <field><id>OUTPUT_BUFFER_ROWS</id><enabled>Y</enabled><name>OUTPUT_BUFFER_ROWS</name></field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field><id>ID_BATCH</id><enabled>Y</enabled><name>ID_BATCH</name></field>
        <field><id>CHANNEL_ID</id><enabled>Y</enabled><name>CHANNEL_ID</name></field>
        <field><id>LOG_DATE</id><enabled>Y</enabled><name>LOG_DATE</name></field>
        <field><id>LOGGING_OBJECT_TYPE</id><enabled>Y</enabled><name>LOGGING_OBJECT_TYPE</name></field>
        <field><id>OBJECT_NAME</id><enabled>Y</enabled><name>OBJECT_NAME</name></field>
        <field><id>OBJECT_COPY</id><enabled>Y</enabled><name>OBJECT_COPY</name></field>
        <field><id>REPOSITORY_DIRECTORY</id><enabled>Y</enabled><name>REPOSITORY_DIRECTORY</name></field>
        <field><id>FILENAME</id><enabled>Y</enabled><name>FILENAME</name></field>
        <field><id>OBJECT_ID</id><enabled>Y</enabled><name>OBJECT_ID</name></field>
        <field><id>OBJECT_REVISION</id><enabled>Y</enabled><name>OBJECT_REVISION</name></field>
        <field><id>PARENT_CHANNEL_ID</id><enabled>Y</enabled><name>PARENT_CHANNEL_ID</name></field>
        <field><id>ROOT_CHANNEL_ID</id><enabled>Y</enabled><name>ROOT_CHANNEL_ID</name></field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field><id>ID_BATCH</id><enabled>Y</enabled><name>ID_BATCH</name></field>
        <field><id>CHANNEL_ID</id><enabled>Y</enabled><name>CHANNEL_ID</name></field>
        <field><id>LOG_DATE</id><enabled>Y</enabled><name>LOG_DATE</name></field>
        <field><id>TRANSNAME</id><enabled>Y</enabled><name>TRANSNAME</name></field>
        <field><id>STEPNAME</id><enabled>Y</enabled><name>STEPNAME</name></field>
        <field><id>STEP_COPY</id><enabled>Y</enabled><name>STEP_COPY</name></field>
        <field><id>LINES_READ</id><enabled>Y</enabled><name>LINES_READ</name></field>
        <field><id>LINES_WRITTEN</id><enabled>Y</enabled><name>LINES_WRITTEN</name></field>
        <field><id>LINES_UPDATED</id><enabled>Y</enabled><name>LINES_UPDATED</name></field>
        <field><id>LINES_INPUT</id><enabled>Y</enabled><name>LINES_INPUT</name></field>
        <field><id>LINES_OUTPUT</id><enabled>Y</enabled><name>LINES_OUTPUT</name></field>
        <field><id>LINES_REJECTED</id><enabled>Y</enabled><name>LINES_REJECTED</name></field>
        <field><id>ERRORS</id><enabled>Y</enabled><name>ERRORS</name></field>
        <field><id>LOG_FIELD</id><enabled>N</enabled><name>LOG_FIELD</name></field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field><id>ID_BATCH</id><enabled>Y</enabled><name>ID_BATCH</name></field>
        <field><id>CHANNEL_ID</id><enabled>Y</enabled><name>CHANNEL_ID</name></field>
        <field><id>LOG_DATE</id><enabled>Y</enabled><name>LOG_DATE</name></field>
        <field><id>METRICS_DATE</id><enabled>Y</enabled><name>METRICS_DATE</name></field>
        <field><id>METRICS_CODE</id><enabled>Y</enabled><name>METRICS_CODE</name></field>
        <field><id>METRICS_DESCRIPTION</id><enabled>Y</enabled><name>METRICS_DESCRIPTION</name></field>
        <field><id>METRICS_SUBJECT</id><enabled>Y</enabled><name>METRICS_SUBJECT</name></field>
        <field><id>METRICS_TYPE</id><enabled>Y</enabled><name>METRICS_TYPE</name></field>
        <field><id>METRICS_VALUE</id><enabled>Y</enabled><name>METRICS_VALUE</name></field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2024/07/30 09:00:00.000</created_date>
    <modified_user>-</modified_user>
    <modified_date>2024/07/30 09:00:00.000</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAADLw==</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>同步mpe_config表数据
从DM数据库读取数据并写入MySQL数据库</note>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <width>300</width>
      <heigth>60</heigth>
      <fontname>Microsoft YaHei</fontname>
      <fontsize>10</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>0</fontcolorred>
      <fontcolorgreen>0</fontcolorgreen>
      <fontcolorblue>0</fontcolorblue>
      <backgroundcolorred>255</backgroundcolorred>
      <backgroundcolorgreen>255</backgroundcolorgreen>
      <backgroundcolorblue>0</backgroundcolorblue>
      <bordercolorred>100</bordercolorred>
      <bordercolorgreen>100</bordercolorgreen>
      <bordercolorblue>100</bordercolorblue>
      <drawshadow>Y</drawshadow>
    </notepad>
  </notepads>
  <connection>
    <name>DM_Connection</name>
    <server>${DM_HOST}</server>
    <type>DM</type>
    <access>Native</access>
    <database>DMSERVER</database>
    <port>${DM_PORT}</port>
    <username>${DM_USER}</username>
    <password>${DM_PASSWORD}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>IS_CLUSTERED</code><attribute>N</attribute></attribute>
      <attribute><code>PORT_NUMBER</code><attribute>${DM_PORT}</attribute></attribute>
      <attribute><code>PRESERVE_RESERVED_WORD_CASE</code><attribute>Y</attribute></attribute>
      <attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute>
      <attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>USE_POOLING</code><attribute>N</attribute></attribute>
    </attributes>
  </connection>
  <connection>
    <name>MySQL_Connection</name>
    <server>${MYSQL_HOST}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${MYSQL_DATABASE}</database>
    <port>${MYSQL_PORT}</port>
    <username>${MYSQL_USER}</username>
    <password>${MYSQL_PASSWORD}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute><code>EXTRA_OPTION_MYSQL.defaultFetchSize</code><attribute>500</attribute></attribute>
      <attribute><code>EXTRA_OPTION_MYSQL.useCursorFetch</code><attribute>true</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>IS_CLUSTERED</code><attribute>N</attribute></attribute>
      <attribute><code>PORT_NUMBER</code><attribute>${MYSQL_PORT}</attribute></attribute>
      <attribute><code>PRESERVE_RESERVED_WORD_CASE</code><attribute>N</attribute></attribute>
      <attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute>
      <attribute><code>STREAM_RESULTS</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>USE_POOLING</code><attribute>N</attribute></attribute>
    </attributes>
  </connection>
  <order>
    <hop><from>Read from DM</from><to>Write to MySQL</to><enabled>Y</enabled></hop>
  </order>
  <step>
    <name>Read from DM</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>DM_Connection</connection>
    <sql>SELECT
    uid,
    ex_id,
    examiner_num,
    layout_time,
    examination_num,
    seatting_way,
    first_seating,
    is_seat_unify,
    is_seatway_unify,
    tour_rule,
    examinee_percent,
    city_opentime,
    county_opentime,
    exampoint_opentime,
    layout_number,
    layout_begin_time,
    parent_uid,
    enable_children_orglevel,
    examinee_data_status,
    status,
    create_by,
    create_time,
    update_by,
    update_time
FROM mpe_config
WHERE status = 'A'</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>200</xloc>
      <yloc>150</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Write to MySQL</name>
    <type>TableOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>MySQL_Connection</connection>
    <schema/>
    <table>mpe_config</table>
    <commit>1000</commit>
    <truncate>Y</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <specify_fields>Y</specify_fields>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_field/>
    <partitioning_daily>N</partitioning_daily>
    <partitioning_monthly>Y</partitioning_monthly>
    <tablename_in_field>N</tablename_in_field>
    <tablename_field/>
    <tablename_in_table>Y</tablename_in_table>
    <return_keys>N</return_keys>
    <return_field/>
    <fields>
      <field><column_name>uid</column_name><stream_name>uid</stream_name></field>
      <field><column_name>ex_id</column_name><stream_name>ex_id</stream_name></field>
      <field><column_name>examiner_num</column_name><stream_name>examiner_num</stream_name></field>
      <field><column_name>layout_time</column_name><stream_name>layout_time</stream_name></field>
      <field><column_name>examination_num</column_name><stream_name>examination_num</stream_name></field>
      <field><column_name>seatting_way</column_name><stream_name>seatting_way</stream_name></field>
      <field><column_name>first_seating</column_name><stream_name>first_seating</stream_name></field>
      <field><column_name>is_seat_unify</column_name><stream_name>is_seat_unify</stream_name></field>
      <field><column_name>is_seatway_unify</column_name><stream_name>is_seatway_unify</stream_name></field>
      <field><column_name>tour_rule</column_name><stream_name>tour_rule</stream_name></field>
      <field><column_name>examinee_percent</column_name><stream_name>examinee_percent</stream_name></field>
      <field><column_name>city_opentime</column_name><stream_name>city_opentime</stream_name></field>
      <field><column_name>county_opentime</column_name><stream_name>county_opentime</stream_name></field>
      <field><column_name>exampoint_opentime</column_name><stream_name>exampoint_opentime</stream_name></field>
      <field><column_name>layout_number</column_name><stream_name>layout_number</stream_name></field>
      <field><column_name>layout_begin_time</column_name><stream_name>layout_begin_time</stream_name></field>
      <field><column_name>parent_uid</column_name><stream_name>parent_uid</stream_name></field>
      <field><column_name>enable_children_orglevel</column_name><stream_name>enable_children_orglevel</stream_name></field>
      <field><column_name>examinee_data_status</column_name><stream_name>examinee_data_status</stream_name></field>
      <field><column_name>status</column_name><stream_name>status</stream_name></field>
      <field><column_name>create_by</column_name><stream_name>create_by</stream_name></field>
      <field><column_name>create_time</column_name><stream_name>create_time</stream_name></field>
      <field><column_name>update_by</column_name><stream_name>update_by</stream_name></field>
      <field><column_name>update_time</column_name><stream_name>update_time</stream_name></field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>400</xloc>
      <yloc>150</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
